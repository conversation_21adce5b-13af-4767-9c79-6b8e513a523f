import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';

interface AuthenticatedRequest {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
  };
}
import { GoodsReceiptService } from './services/goods-receipt.service';
import { CreateGoodsReceiptDto } from './dto/create-goods-receipt.dto';
import { UpdateGoodsReceiptDto } from './dto/update-goods-receipt.dto';
import { GoodsReceiptQueryDto, GoodsReceiptStatusUpdateDto } from './dto/goods-receipt-query.dto';
import { GoodsReceiptStatsQueryDto } from './dto/goods-receipt-stats.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../settings/guards/admin.guard';
import { ManagerGuard } from '../suppliers/guards/manager.guard';

@Controller('goods-receipts')
@UseGuards(JwtAuthGuard)
export class GoodsReceiptController {
  constructor(private readonly goodsReceiptService: GoodsReceiptService) { }

  @Post()
  @UseGuards(ManagerGuard) // Admin or Pharmacist can create
  async create(@Body() createGoodsReceiptDto: CreateGoodsReceiptDto, @Request() req: AuthenticatedRequest) {
    return this.goodsReceiptService.create(createGoodsReceiptDto, req.user.id);
  }

  @Get()
  async findAll(@Query() query: GoodsReceiptQueryDto) {
    return this.goodsReceiptService.findAll(query);
  }

  @Get('stats')
  async getStats(@Query() query: GoodsReceiptStatsQueryDto) {
    return this.goodsReceiptService.getStats(query.period);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.goodsReceiptService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update
  async update(
    @Param('id') id: string,
    @Body() updateGoodsReceiptDto: UpdateGoodsReceiptDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.goodsReceiptService.update(id, updateGoodsReceiptDto, req.user.id);
  }

  @Patch(':id/status')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can update status
  async updateStatus(
    @Param('id') id: string,
    @Body() statusDto: GoodsReceiptStatusUpdateDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.goodsReceiptService.updateStatus(id, statusDto, req.user.id);
  }

  @Post(':id/approve')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can approve
  async approve(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.goodsReceiptService.approve(id, req.user.id);
  }

  @Post(':id/reject')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can reject
  async reject(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.goodsReceiptService.reject(id, req.user.id);
  }

  @Post(':id/complete')
  @UseGuards(ManagerGuard) // Admin or Pharmacist can complete
  async complete(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.goodsReceiptService.complete(id, req.user.id);
  }

  @Delete(':id')
  @UseGuards(AdminGuard) // Only Admin can hard delete
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.goodsReceiptService.remove(id);
  }
}
