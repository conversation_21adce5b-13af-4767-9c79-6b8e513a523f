/**
 * Procurement-related interfaces and types
 */
import { PurchaseOrderStatus, GoodsReceiptStatus } from '@prisma/client';
import { TaxType } from '../dto/tax-configuration.dto';

export interface NumberGenerationOptions {
  prefix: string;
  dateFormat?: string;
  sequenceLength?: number;
}

export interface GeneratedNumber {
  number: string;
  sequence: number;
}

export interface PurchaseOrderQueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  supplierId?: string;
  status?: PurchaseOrderStatus;
  orderDateFrom?: string;
  orderDateTo?: string;
  expectedDeliveryFrom?: string;
  expectedDeliveryTo?: string;
  createdBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface GoodsReceiptQueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  supplierId?: string;
  purchaseOrderId?: string;
  status?: GoodsReceiptStatus;
  receiptDateFrom?: string;
  receiptDateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PurchaseOrderApprovalData {
  approvedBy: string;
  approvalNotes?: string;
  approvedAt?: Date;
}

export interface PurchaseOrderStatusUpdateData {
  status: PurchaseOrderStatus;
  updatedBy: string;
  notes?: string;
  sentAt?: Date;
}

export interface GoodsReceiptProcessingData {
  processedBy: string;
  processedAt: Date;
  notes?: string;
}

export interface ProcurementStats {
  totalPurchaseOrders: number;
  pendingApproval: number;
  inProgress: number;
  completed: number;
  totalValue: number;
  averageOrderValue: number;
  topSuppliers: Array<{
    supplierId: string;
    supplierName: string;
    orderCount: number;
    totalValue: number;
  }>;
}

export interface GoodsReceiptStats {
  totalReceipts: number;
  pending: number;
  approved: number;
  rejected: number;
}

// Tax-related interfaces
export interface TaxConfiguration {
  taxType: TaxType;
  taxRate: number;
  isActive: boolean;
  effectiveFrom: Date;
  effectiveTo?: Date;
  description?: string;
}

export interface TaxCalculationOptions {
  taxType?: TaxType;
  isInclusive?: boolean;
  exemptItems?: string[];
}

export interface TaxCalculationResult {
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  taxRate: number;
  taxType: TaxType;
  isInclusive: boolean;
}
