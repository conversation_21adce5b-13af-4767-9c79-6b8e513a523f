describe('<PERSON>ur Ke<PERSON>', () => {
  beforeEach(() => {
    // Setup API interceptors
    cy.setupApiInterceptors()
    
    // Login as admin
    cy.loginAsAdmin()
    
    // Seed test data
    cy.seedSuppliers(3)
    cy.seedProducts(10)
  })

  afterEach(() => {
    // Clean up test data
    cy.cleanTestData()
  })

  describe('Purchase Order Management', () => {
    beforeEach(() => {
      cy.visit('/dashboard/purchase-orders')
      cy.waitForLoadingToFinish()
    })

    it('should create new purchase order successfully', () => {
      const purchaseOrder = {
        supplierName: 'Test Supplier',
        expectedDeliveryDate: '2024-12-31',
        notes: 'Test purchase order untuk E2E testing',
      }

      // Click create PO button
      cy.get('[data-testid="create-po-button"]').click()
      
      // Verify modal opened
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Buat Purchase Order Baru').should('be.visible')
      
      // Select supplier
      cy.selectDropdownOption('[name="supplierId"]', purchaseOrder.supplierName)
      
      // Set expected delivery date
      cy.selectDate('[name="expectedDeliveryDate"]', '31')
      
      // Add notes
      cy.get('[name="notes"]').type(purchaseOrder.notes)
      
      // Add product items
      cy.get('[data-testid="add-item-button"]').click()
      
      // Select product
      cy.selectDropdownOption('[name="items.0.productId"]', 'Paracetamol 500mg')
      
      // Set quantity
      cy.get('[name="items.0.quantity"]').type('100')
      
      // Set unit price
      cy.get('[name="items.0.unitPrice"]').type('5000')
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@createPurchaseOrder')
      
      // Verify success message
      cy.checkToast('Purchase Order berhasil dibuat', 'success')
      
      // Verify PO appears in list
      cy.contains(purchaseOrder.supplierName).should('be.visible')
    })

    it('should validate purchase order form', () => {
      // Click create PO button
      cy.get('[data-testid="create-po-button"]').click()
      
      // Try to submit empty form
      cy.get('button[type="submit"]').click()
      
      // Check validation errors
      cy.contains('Supplier wajib dipilih').should('be.visible')
      cy.contains('Tanggal pengiriman wajib diisi').should('be.visible')
      cy.contains('Minimal satu item harus ditambahkan').should('be.visible')
    })

    it('should approve purchase order', () => {
      // Click approve button on first PO
      cy.get('table tbody tr:first-child [data-testid="approve-po-button"]').click()
      
      // Verify confirmation dialog
      cy.get('[role="alertdialog"]').should('be.visible')
      cy.contains('Setujui Purchase Order').should('be.visible')
      
      // Confirm approval
      cy.get('[data-testid="confirm-approve-button"]').click()
      
      // Wait for API call
      cy.wait('@approvePurchaseOrder')
      
      // Verify success message
      cy.checkToast('Purchase Order berhasil disetujui', 'success')
      
      // Verify status changed
      cy.get('table tbody tr:first-child').should('contain.text', 'Disetujui')
    })

    it('should reject purchase order with reason', () => {
      const rejectionReason = 'Harga terlalu tinggi'
      
      // Click reject button on first PO
      cy.get('table tbody tr:first-child [data-testid="reject-po-button"]').click()
      
      // Verify rejection modal
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Tolak Purchase Order').should('be.visible')
      
      // Enter rejection reason
      cy.get('[name="rejectionReason"]').type(rejectionReason)
      
      // Confirm rejection
      cy.get('[data-testid="confirm-reject-button"]').click()
      
      // Wait for API call
      cy.wait('@rejectPurchaseOrder')
      
      // Verify success message
      cy.checkToast('Purchase Order berhasil ditolak', 'success')
      
      // Verify status changed
      cy.get('table tbody tr:first-child').should('contain.text', 'Ditolak')
    })
  })

  describe('Goods Receipt Management', () => {
    beforeEach(() => {
      cy.visit('/dashboard/goods-receipts')
      cy.waitForLoadingToFinish()
    })

    it('should create goods receipt from approved PO', () => {
      // Click create goods receipt button
      cy.get('[data-testid="create-gr-button"]').click()
      
      // Select approved PO
      cy.selectDropdownOption('[name="purchaseOrderId"]', 'PO-001')
      
      // Verify PO details loaded
      cy.get('[data-testid="po-details"]').should('be.visible')
      
      // Update received quantities
      cy.get('[name="items.0.receivedQuantity"]').clear().type('95')
      
      // Add batch information
      cy.get('[name="items.0.batchNumber"]').type('BATCH-001')
      cy.selectDate('[name="items.0.expiryDate"]', '31')
      
      // Add notes
      cy.get('[name="notes"]').type('5 unit rusak dalam pengiriman')
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@createGoodsReceipt')
      
      // Verify success message
      cy.checkToast('Penerimaan barang berhasil dicatat', 'success')
    })

    it('should validate batch information', () => {
      // Click create goods receipt button
      cy.get('[data-testid="create-gr-button"]').click()
      
      // Select approved PO
      cy.selectDropdownOption('[name="purchaseOrderId"]', 'PO-001')
      
      // Try to submit without batch info
      cy.get('button[type="submit"]').click()
      
      // Check validation errors
      cy.contains('Nomor batch wajib diisi').should('be.visible')
      cy.contains('Tanggal kadaluarsa wajib diisi').should('be.visible')
    })

    it('should handle partial receipt', () => {
      // Click create goods receipt button
      cy.get('[data-testid="create-gr-button"]').click()
      
      // Select approved PO
      cy.selectDropdownOption('[name="purchaseOrderId"]', 'PO-001')
      
      // Set partial quantity
      cy.get('[name="items.0.receivedQuantity"]').clear().type('50')
      
      // Add batch information
      cy.get('[name="items.0.batchNumber"]').type('BATCH-PARTIAL')
      cy.selectDate('[name="items.0.expiryDate"]', '31')
      
      // Mark as partial receipt
      cy.get('[name="isPartialReceipt"]').check()
      
      // Submit form
      cy.get('button[type="submit"]').click()
      
      // Wait for API call
      cy.wait('@createGoodsReceipt')
      
      // Verify success message
      cy.checkToast('Penerimaan parsial berhasil dicatat', 'success')
      
      // Verify PO status remains partially received
      cy.visit('/dashboard/purchase-orders')
      cy.waitForLoadingToFinish()
      cy.get('table tbody tr:first-child').should('contain.text', 'Sebagian Diterima')
    })
  })

  describe('Real-time Batch Validation', () => {
    beforeEach(() => {
      cy.visit('/dashboard/goods-receipts/new')
      cy.waitForLoadingToFinish()
    })

    it('should validate batch number in real-time', () => {
      const batchData = {
        batchNumber: 'BATCH-TEST-001',
        productId: 'product-1',
      }

      // Select a product first
      cy.selectDropdownOption('[name="items.0.productId"]', 'Paracetamol 500mg')
      
      // Enter batch number
      cy.get('[name="items.0.batchNumber"]').type(batchData.batchNumber)
      
      // Wait for real-time validation
      cy.wait('@validateBatchRealTime')
      
      // Verify validation result
      cy.get('[data-testid="batch-validation-result"]').should('be.visible')
      cy.get('[data-testid="batch-validation-result"]').should('contain.text', 'Batch number valid')
    })

    it('should show warning for duplicate batch number', () => {
      const existingBatch = 'BATCH-EXISTING-001'
      
      // Mock API response for existing batch
      cy.mockApiResponse('POST', '/api/procurement/batch-validation/real-time', {
        isValid: false,
        isDuplicate: true,
        message: 'Batch number sudah ada untuk produk ini',
        existingBatch: {
          batchNumber: existingBatch,
          expiryDate: '2025-12-31',
          quantityOnHand: 50,
        },
      })
      
      // Select a product
      cy.selectDropdownOption('[name="items.0.productId"]', 'Paracetamol 500mg')
      
      // Enter existing batch number
      cy.get('[name="items.0.batchNumber"]').type(existingBatch)
      
      // Wait for validation
      cy.wait('@validateBatchRealTime')
      
      // Verify warning message
      cy.get('[data-testid="batch-validation-warning"]').should('be.visible')
      cy.contains('Batch number sudah ada untuk produk ini').should('be.visible')
      
      // Verify existing batch info is shown
      cy.get('[data-testid="existing-batch-info"]').should('be.visible')
      cy.contains('Stok saat ini: 50').should('be.visible')
    })

    it('should validate expiry date against batch standards', () => {
      // Select a product
      cy.selectDropdownOption('[name="items.0.productId"]', 'Paracetamol 500mg')
      
      // Enter batch number
      cy.get('[name="items.0.batchNumber"]').type('BATCH-EXPIRY-TEST')
      
      // Set expiry date too close to current date
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      cy.selectDate('[name="items.0.expiryDate"]', tomorrow.getDate().toString())
      
      // Check validation warning
      cy.contains('Tanggal kadaluarsa terlalu dekat').should('be.visible')
      cy.get('[data-testid="expiry-warning"]').should('be.visible')
    })
  })

  describe('Batch Management Integration', () => {
    beforeEach(() => {
      cy.visit('/dashboard/batch-management')
      cy.waitForLoadingToFinish()
    })

    it('should view batch details from goods receipt', () => {
      // Click on a batch entry
      cy.get('table tbody tr:first-child [data-testid="view-batch-button"]').click()
      
      // Verify batch detail modal
      cy.get('[role="dialog"]').should('be.visible')
      cy.contains('Detail Batch').should('be.visible')
      
      // Check batch information
      cy.get('[data-testid="batch-number"]').should('not.be.empty')
      cy.get('[data-testid="product-name"]').should('not.be.empty')
      cy.get('[data-testid="expiry-date"]').should('not.be.empty')
      cy.get('[data-testid="quantity-on-hand"]').should('not.be.empty')
      
      // Check goods receipt history
      cy.get('[data-testid="receipt-history"]').should('be.visible')
    })

    it('should filter batches by expiry status', () => {
      // Open expiry filter
      cy.get('[data-testid="expiry-filter"]').click()
      
      // Select expired batches
      cy.selectDropdownOption('[data-testid="expiry-filter"]', 'Kadaluarsa')
      
      // Wait for filter to apply
      cy.waitForLoadingToFinish()
      
      // Verify filtered results show only expired batches
      cy.get('table tbody tr').each(($row) => {
        cy.wrap($row).find('[data-testid="expiry-status"]').should('contain.text', 'Kadaluarsa')
      })
    })

    it('should search batches by batch number', () => {
      const batchNumber = 'BATCH-001'
      
      // Use search input
      cy.get('[data-testid="batch-search"]').type(batchNumber)
      
      // Wait for search results
      cy.waitForLoadingToFinish()
      
      // Verify search results
      cy.get('table tbody tr').each(($row) => {
        cy.wrap($row).should('contain.text', batchNumber)
      })
    })
  })

  describe('Procurement Reports', () => {
    beforeEach(() => {
      cy.visit('/dashboard/reports/procurement')
      cy.waitForLoadingToFinish()
    })

    it('should generate purchase order report', () => {
      // Set date range
      cy.selectDate('[name="startDate"]', '1')
      cy.selectDate('[name="endDate"]', '31')
      
      // Select report type
      cy.selectDropdownOption('[name="reportType"]', 'Purchase Orders')
      
      // Generate report
      cy.get('[data-testid="generate-report-button"]').click()
      
      // Wait for report generation
      cy.waitForLoadingToFinish()
      
      // Verify report data
      cy.get('[data-testid="report-table"]').should('be.visible')
      cy.get('[data-testid="report-summary"]').should('be.visible')
      
      // Export report
      cy.get('[data-testid="export-report-button"]').click()
      cy.selectDropdownOption('[data-testid="export-format"]', 'PDF')
      cy.get('[data-testid="confirm-export-button"]').click()
      
      // Verify download
      cy.readFile('cypress/downloads/laporan-purchase-order.pdf').should('exist')
    })

    it('should generate goods receipt report', () => {
      // Set date range
      cy.selectDate('[name="startDate"]', '1')
      cy.selectDate('[name="endDate"]', '31')
      
      // Select report type
      cy.selectDropdownOption('[name="reportType"]', 'Goods Receipts')
      
      // Generate report
      cy.get('[data-testid="generate-report-button"]').click()
      
      // Wait for report generation
      cy.waitForLoadingToFinish()
      
      // Verify report shows goods receipt data
      cy.get('[data-testid="report-table"]').should('be.visible')
      cy.contains('Total Penerimaan').should('be.visible')
      cy.contains('Nilai Total').should('be.visible')
    })
  })
})
