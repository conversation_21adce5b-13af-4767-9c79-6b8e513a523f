{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "type-check": "tsc --noEmit", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:component": "cypress run --component", "cypress:open:component": "cypress open --component", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:component": "cypress run --component", "test:component:open": "cypress open --component"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.80.6", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.514.0", "next": "15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "postcss": "^8.5.4", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "use-debounce": "^10.0.5", "use-immer": "^0.11.0", "vaul": "^1.1.2", "zod": "^3.25.63"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.19.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "cypress": "^13.6.3", "eslint": "^8.57.1", "eslint-config-next": "15.3.3", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vitest": "^3.2.3"}}