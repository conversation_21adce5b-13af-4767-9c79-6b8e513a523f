// Batch Management Types
export interface BatchManagementQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: BatchStatus;

  productId?: string;
  supplierId?: string;
  expiryDateFrom?: string;
  expiryDateTo?: string;
  batchNumber?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface BatchManagementStats {
  totalBatches: number;
  activeBatches: number;
  expiringSoon: number;
  expired: number;
  validationErrors: number;
  bpomCompliant: number; // Number of batches with valid BPOM batch number format
  recentActivity: number;

  validationSuccessRate: number;
}

export interface BatchHistoryItem {
  id: string;
  batchNumber: string;
  productId: string;
  productName: string;
  supplierId?: string;
  supplierName?: string;
  status: BatchStatus;

  expiryDate?: Date | string;
  manufacturingDate?: Date | string;
  quantityOnHand: number;
  quantityAllocated: number;
  location?: string;
  receivedDate: Date | string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface BatchValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  validationLevel: ValidationLevel;
  bpomCompliant: boolean; // Whether batch number format complies with BPOM standards
  uniquenessCheck: {
    isUnique: boolean;
    conflictingBatches?: ConflictingBatch[];
  };
  formatValidation: {
    passedRules: string[];
    failedRules: string[];
    recommendedFormat?: string;
  };
  auditTrail?: {
    validationId: string;
    timestamp: Date | string;
    userId: string;
  };
}

export interface ConflictingBatch {
  id: string;
  batchNumber: string;
  productName: string;
  supplierName: string;
  createdAt: Date | string;
}



export interface BatchAuditLogItem {
  id: string;
  batchNumber: string;
  action: BatchAuditAction;
  status: 'SUCCESS' | 'FAILED' | 'WARNING';
  productId?: string;
  productName?: string;
  supplierId?: string;
  supplierName?: string;
  userId?: string;
  userName?: string;
  referenceType?: string;
  referenceId?: string;
  referenceNumber?: string;
  validationRules?: string[];
  validationResults?: any;
  bpomCompliant?: boolean; // Whether batch number format complies with BPOM standards
  complianceNotes?: string; // Notes about BPOM batch number validation compliance
  complianceLevel?: string; // Level of BPOM batch number validation compliance
  message?: string;
  details?: string;
  errorMessage?: string;
  createdAt: Date | string;
}

export interface ExpiryTrackingItem {
  id: string;
  batchNumber: string;
  productId: string;
  productName: string;
  supplierId?: string;
  supplierName?: string;
  expiryDate: Date | string;
  daysUntilExpiry: number;
  quantityOnHand: number;
  location?: string;
  status: 'ACTIVE' | 'EXPIRING_SOON' | 'EXPIRED';
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

// API Response Types
export interface BatchManagementResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Enums
export type BatchStatus =
  | 'ACTIVE'
  | 'EXPIRED'
  | 'EXPIRING_SOON'
  | 'RECALLED'
  | 'QUARANTINE'
  | 'DISPOSED';



export type ValidationLevel =
  | 'BASIC'           // Basic format validation
  | 'STANDARD'        // Standard pharmaceutical validation
  | 'BPOM_COMPLIANT'  // BPOM batch number format validation
  | 'CONTROLLED';     // Controlled substance validation

export type BatchAuditAction =
  | 'CREATED'
  | 'UPDATED'
  | 'VALIDATED'
  | 'REJECTED'
  | 'SUBSTITUTED'
  | 'EXPIRED'
  | 'RECALLED'

  | 'FORMAT_VALIDATION'
  | 'UNIQUENESS_CHECK'
  | 'GOODS_RECEIPT'
  | 'INVENTORY_CREATED'
  | 'STOCK_MOVEMENT';

// Form DTOs
export interface ValidateBatchNumberDto {
  batchNumber: string;
  productId: string;
  supplierId?: string;
  expiryDate?: string;
  manufacturingDate?: string;
  isSubstitution?: boolean;
  originalBatchNumber?: string;
}



export interface BatchExportParams {
  format: 'CSV' | 'EXCEL' | 'PDF';
  filters?: BatchManagementQueryParams;
  includeAuditTrail?: boolean;

}
