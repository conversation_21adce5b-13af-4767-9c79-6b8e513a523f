import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { goodsReceiptsApi } from '@/lib/api/goods-receipts';
import {
  GoodsReceiptQueryParams,
  CreateGoodsReceiptDto,
  UpdateGoodsReceiptDto,
  GoodsReceiptStatusUpdateDto,

} from '@/types/goods-receipt';

// Query keys factory
export const goodsReceiptKeys = {
  all: ['goods-receipts'] as const,
  lists: () => [...goodsReceiptKeys.all, 'list'] as const,
  list: (params: GoodsReceiptQueryParams) => [...goodsReceiptKeys.lists(), params] as const,
  details: () => [...goodsReceiptKeys.all, 'detail'] as const,
  detail: (id: string) => [...goodsReceiptKeys.details(), id] as const,
  stats: () => [...goodsReceiptKeys.all, 'stats'] as const,

  readyForCompletion: () => [...goodsReceiptKeys.all, 'ready-for-completion'] as const,
  byPurchaseOrder: (purchaseOrderId: string) => [...goodsReceiptKeys.all, 'by-purchase-order', purchaseOrderId] as const,
  discrepancyReport: (id: string) => [...goodsReceiptKeys.detail(id), 'discrepancy-report'] as const,

};

// Query hooks
export function useGoodsReceipts(params: GoodsReceiptQueryParams) {
  return useQuery({
    queryKey: goodsReceiptKeys.list(params),
    queryFn: () => goodsReceiptsApi.getGoodsReceipts(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: (previousData) => previousData, // Keep previous data while loading
  });
}

export function useGoodsReceipt(id: string) {
  return useQuery({
    queryKey: goodsReceiptKeys.detail(id),
    queryFn: () => goodsReceiptsApi.getGoodsReceipt(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });
}

export function useGoodsReceiptStats(period?: string) {
  return useQuery({
    queryKey: [...goodsReceiptKeys.stats(), period],
    queryFn: () => goodsReceiptsApi.getGoodsReceiptStats(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}



export function useReadyForCompletion() {
  return useQuery({
    queryKey: goodsReceiptKeys.readyForCompletion(),
    queryFn: () => goodsReceiptsApi.getReadyForCompletion(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useGoodsReceiptsByPurchaseOrder(purchaseOrderId: string) {
  return useQuery({
    queryKey: goodsReceiptKeys.byPurchaseOrder(purchaseOrderId),
    queryFn: () => goodsReceiptsApi.getGoodsReceiptsByPurchaseOrder(purchaseOrderId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!purchaseOrderId,
  });
}

export function useDiscrepancyReport(id: string) {
  return useQuery({
    queryKey: goodsReceiptKeys.discrepancyReport(id),
    queryFn: () => goodsReceiptsApi.getDiscrepancyReport(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });
}



// Invalidation helper
export function useGoodsReceiptsInvalidate() {
  const queryClient = useQueryClient();
  return () => queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.all });
}

// Mutation hooks
export function useCreateGoodsReceipt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateGoodsReceiptDto) => goodsReceiptsApi.createGoodsReceipt(data),
    onSuccess: (data) => {
      // Invalidate and refetch goods receipts list
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });

      // Set the new goods receipt data in cache
      queryClient.setQueryData(goodsReceiptKeys.detail(data.id), data);

      toast.success('Penerimaan barang berhasil dibuat');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat penerimaan barang';
      toast.error(message);
    },
  });
}

export function useUpdateGoodsReceipt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateGoodsReceiptDto }) =>
      goodsReceiptsApi.updateGoodsReceipt(id, data),
    onSuccess: (data) => {
      // Update the goods receipt data in cache
      queryClient.setQueryData(goodsReceiptKeys.detail(data.id), data);

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });

      toast.success('Penerimaan barang berhasil diperbarui');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui penerimaan barang';
      toast.error(message);
    },
  });
}

export function useDeleteGoodsReceipt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => goodsReceiptsApi.deleteGoodsReceipt(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: goodsReceiptKeys.detail(id) });

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });

      toast.success('Penerimaan barang berhasil dihapus');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus penerimaan barang';
      toast.error(message);
    },
  });
}



export function useUpdateGoodsReceiptStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: GoodsReceiptStatusUpdateDto }) =>
      goodsReceiptsApi.updateStatus(id, data),
    onSuccess: (data) => {
      // Update the goods receipt data in cache
      queryClient.setQueryData(goodsReceiptKeys.detail(data.id), data);

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });

      toast.success('Status penerimaan barang berhasil diperbarui');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui status penerimaan barang';
      toast.error(message);
    },
  });
}

export function useApproveGoodsReceipt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => goodsReceiptsApi.approveGoodsReceipt(id),
    onSuccess: (data) => {
      // Update the goods receipt data in cache
      queryClient.setQueryData(goodsReceiptKeys.detail(data.id), data);

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.readyForCompletion() });

      toast.success('Penerimaan barang berhasil disetujui');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menyetujui penerimaan barang';
      toast.error(message);
    },
  });
}

export function useRejectGoodsReceipt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) =>
      goodsReceiptsApi.rejectGoodsReceipt(id),
    onSuccess: (data) => {
      // Update the goods receipt data in cache
      queryClient.setQueryData(goodsReceiptKeys.detail(data.id), data);

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });

      toast.success('Penerimaan barang berhasil ditolak');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menolak penerimaan barang';
      toast.error(message);
    },
  });
}

export function useCompleteGoodsReceipt() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => goodsReceiptsApi.completeGoodsReceipt(id),
    onSuccess: (data) => {
      // Update the goods receipt data in cache
      queryClient.setQueryData(goodsReceiptKeys.detail(data.id), data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.readyForCompletion() });

      // Also invalidate inventory queries since new inventory items are created
      queryClient.invalidateQueries({ queryKey: ['inventory'] });

      toast.success('Penerimaan barang berhasil diselesaikan dan stok telah diperbarui');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menyelesaikan penerimaan barang';
      toast.error(message);
    },
  });
}

export function useCreateFromPurchaseOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (purchaseOrderId: string) => goodsReceiptsApi.createFromPurchaseOrder(purchaseOrderId),
    onSuccess: (data) => {
      // Set the new goods receipt data in cache
      queryClient.setQueryData(goodsReceiptKeys.detail(data.id), data);

      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });

      toast.success('Penerimaan barang berhasil dibuat dari purchase order');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat penerimaan barang dari purchase order';
      toast.error(message);
    },
  });
}





export function useExportGoodsReceipts() {
  return useMutation({
    mutationFn: (params: GoodsReceiptQueryParams) => goodsReceiptsApi.exportGoodsReceipts(params),
    onSuccess: (blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `goods-receipts-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('Data penerimaan barang berhasil diekspor');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal mengekspor data penerimaan barang';
      toast.error(message);
    },
  });
}

export function useBulkUpdateGoodsReceiptStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ ids, status }: { ids: string[]; status: string }) =>
      goodsReceiptsApi.bulkUpdateStatus(ids, status),
    onSuccess: (data) => {
      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });

      toast.success(`${data.updated} penerimaan barang berhasil diperbarui`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal memperbarui status penerimaan barang';
      toast.error(message);
    },
  });
}

export function useBulkDeleteGoodsReceipts() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => goodsReceiptsApi.bulkDelete(ids),
    onSuccess: (data) => {
      // Invalidate lists and stats
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.lists() });
      queryClient.invalidateQueries({ queryKey: goodsReceiptKeys.stats() });

      toast.success(`${data.deleted} penerimaan barang berhasil dihapus`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal menghapus penerimaan barang';
      toast.error(message);
    },
  });
}

export function useValidateForCompletion(id: string) {
  return useQuery({
    queryKey: [...goodsReceiptKeys.detail(id), 'validate-completion'],
    queryFn: () => goodsReceiptsApi.validateForCompletion(id),
    enabled: !!id,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
  });
}

export function useGenerateReceiptNumber() {
  return useMutation({
    mutationFn: () => goodsReceiptsApi.generateReceiptNumber(),
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Gagal membuat nomor penerimaan barang';
      toast.error(message);
    },
  });
}

export function useValidateReceiptNumber(number: string) {
  return useQuery({
    queryKey: ['goods-receipts', 'validate-number', number],
    queryFn: () => goodsReceiptsApi.validateReceiptNumber(number),
    enabled: !!number && number.length > 0,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
  });
}
