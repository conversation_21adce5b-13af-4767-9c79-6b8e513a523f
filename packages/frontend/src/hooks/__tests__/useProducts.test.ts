import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useProducts, useProduct, useCreateProduct, useUpdateProduct, useDeleteProduct, productKeys } from '../useProducts'
import { productsApi } from '@/lib/api/products'
import { mockToast, createTestQueryClient, resetTestEnvironment } from '@/test/utils'
import type { ProductQueryParams, Product, CreateProductDto, UpdateProductDto } from '@/types/product'
import { ProductType, ProductCategory, MedicineClassification, UnitType } from '@/types/product'

// Mock the products API
vi.mock('@/lib/api/products', () => ({
  productsApi: {
    getProducts: vi.fn(),
    getProduct: vi.fn(),
    createProduct: vi.fn(),
    updateProduct: vi.fn(),
    deleteProduct: vi.fn(),
  },
}))

const mockProductsApi = vi.mocked(productsApi)

describe('useProducts Hook', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    resetTestEnvironment() // Prevent state leakage
    queryClient = createTestQueryClient()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => {
    const React = require('react')
    return React.createElement(QueryClientProvider, { client: queryClient }, children)
  }

  describe('useProducts', () => {
    it('should fetch products successfully', async () => {
      const mockProducts = {
        data: [
          {
            id: '1',
            code: 'PRD-001',
            name: 'Test Product',
            genericName: 'Generic Test',
            type: ProductType.MEDICINE,
            category: ProductCategory.ANALGESIC,
            manufacturer: 'Test Manufacturer',
            medicineClassification: MedicineClassification.OBAT_BEBAS,
            baseUnitId: 'unit-1',
            description: 'Test Description',
            isActive: true,
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
            baseUnit: {
              id: 'unit-1',
              name: 'Tablet',
              abbreviation: 'tab',
              type: UnitType.COUNT,
              isBaseUnit: true,
              isActive: true,
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z',
            },
            unitHierarchies: [],
          },
        ],
        meta: { total: 1, page: 1, limit: 10, totalPages: 1, hasNextPage: false, hasPreviousPage: false },
      }

      mockProductsApi.getProducts.mockResolvedValue(mockProducts)

      const params: ProductQueryParams = { page: 1, limit: 10 }
      const { result } = renderHook(() => useProducts(params), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockProducts)
      expect(mockProductsApi.getProducts).toHaveBeenCalledWith(params)
    })

    it('should handle error when fetching products fails', async () => {
      const errorMessage = 'Failed to fetch products'
      mockProductsApi.getProducts.mockRejectedValue(new Error(errorMessage))

      const params: ProductQueryParams = { page: 1, limit: 10 }
      const { result } = renderHook(() => useProducts(params), { wrapper })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
    })

    it('should use correct query key', () => {
      const params: ProductQueryParams = { page: 1, limit: 10, search: 'test' }
      const expectedKey = productKeys.list(params)

      expect(expectedKey).toEqual(['products', 'list', params])
    })
  })

  describe('useProduct', () => {
    it('should fetch single product successfully', async () => {
      const mockProduct: Product = {
        id: '1',
        code: 'PRD-001',
        name: 'Test Product',
        genericName: 'Generic Test',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        manufacturer: 'Test Manufacturer',
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: 'unit-1',
        description: 'Test Description',
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        baseUnit: {
          id: 'unit-1',
          name: 'Tablet',
          abbreviation: 'tab',
          type: UnitType.COUNT,
          isBaseUnit: true,
          isActive: true,
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
        },
        unitHierarchies: [],
      }

      mockProductsApi.getProduct.mockResolvedValue(mockProduct)

      const { result } = renderHook(() => useProduct('1'), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockProduct)
      expect(mockProductsApi.getProduct).toHaveBeenCalledWith('1')
    })

    it('should not fetch when id is empty', () => {
      const { result } = renderHook(() => useProduct(''), { wrapper })

      expect(result.current.fetchStatus).toBe('idle')
      expect(mockProductsApi.getProduct).not.toHaveBeenCalled()
    })
  })

  describe('useCreateProduct', () => {
    it('should create product successfully', async () => {
      const newProduct: CreateProductDto = {
        code: 'PRD-002',
        name: 'New Product',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        manufacturer: 'Test Manufacturer',
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: 'unit-1',
        description: 'New Description',
      }

      const createdProduct: Product = {
        id: '2',
        ...newProduct,
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        baseUnit: {
          id: 'unit-1',
          name: 'Tablet',
          abbreviation: 'tab',
          type: UnitType.COUNT,
          isBaseUnit: true,
          isActive: true,
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
        },
        unitHierarchies: [],
      }

      mockProductsApi.createProduct.mockResolvedValue(createdProduct)

      const { result } = renderHook(() => useCreateProduct(), { wrapper })

      await result.current.mutateAsync(newProduct)

      expect(mockProductsApi.createProduct).toHaveBeenCalledWith(newProduct)
      expect(mockToast.success).toHaveBeenCalledWith('Produk berhasil ditambahkan')
    })

    it('should handle create product error', async () => {
      const newProduct: CreateProductDto = {
        code: 'PRD-002',
        name: 'New Product',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        manufacturer: 'Test Manufacturer',
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: 'unit-1',
        description: 'New Description',
      }

      const errorMessage = 'Failed to create product'
      mockProductsApi.createProduct.mockRejectedValue(new Error(errorMessage))

      const { result } = renderHook(() => useCreateProduct(), { wrapper })

      await expect(result.current.mutateAsync(newProduct)).rejects.toThrow(errorMessage)
      expect(mockToast.error).toHaveBeenCalledWith('Gagal menambahkan produk')
    })
  })

  describe('useUpdateProduct', () => {
    it('should update product successfully', async () => {
      const updateData: UpdateProductDto = {
        name: 'Updated Product',
        description: 'Updated Description',
      }

      const updatedProduct: Product = {
        id: '1',
        code: 'PRD-001',
        name: 'Updated Product',
        genericName: 'Generic Test',
        type: ProductType.MEDICINE,
        category: ProductCategory.ANALGESIC,
        manufacturer: 'Test Manufacturer',
        medicineClassification: MedicineClassification.OBAT_BEBAS,
        baseUnitId: 'unit-1',
        description: 'Updated Description',
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        baseUnit: {
          id: 'unit-1',
          name: 'Tablet',
          abbreviation: 'tab',
          type: UnitType.COUNT,
          isBaseUnit: true,
          isActive: true,
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z',
        },
        unitHierarchies: [],
      }

      mockProductsApi.updateProduct.mockResolvedValue(updatedProduct)

      const { result } = renderHook(() => useUpdateProduct(), { wrapper })

      await result.current.mutateAsync({ id: '1', data: updateData })

      expect(mockProductsApi.updateProduct).toHaveBeenCalledWith('1', updateData)
      expect(mockToast.success).toHaveBeenCalledWith('Produk berhasil diperbarui')
    })
  })

  describe('useDeleteProduct', () => {
    it('should delete product successfully', async () => {
      mockProductsApi.deleteProduct.mockResolvedValue(undefined)

      const { result } = renderHook(() => useDeleteProduct(), { wrapper })

      await result.current.mutateAsync('1')

      expect(mockProductsApi.deleteProduct).toHaveBeenCalledWith('1')
      expect(mockToast.success).toHaveBeenCalledWith('Produk berhasil dihapus')
    })

    it('should handle delete product error', async () => {
      const errorMessage = 'Failed to delete product'
      mockProductsApi.deleteProduct.mockRejectedValue(new Error(errorMessage))

      const { result } = renderHook(() => useDeleteProduct(), { wrapper })

      await expect(result.current.mutateAsync('1')).rejects.toThrow(errorMessage)
      expect(mockToast.error).toHaveBeenCalledWith('Gagal menghapus produk')
    })
  })
})
