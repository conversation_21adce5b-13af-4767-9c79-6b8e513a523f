'use client';

import * as React from 'react';
import { Check, ChevronsUpDown, Search, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Product, ProductQueryParams } from '@/types/product';
import { useProducts, useProduct } from '@/hooks/useProducts';
import { useDebounce, DEBOUNCE_DELAYS } from '@/hooks/useDebounce';

interface ProductSelectorProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  emptyMessage?: string;
  searchPlaceholder?: string;
  includeInactive?: boolean;
}

export interface ProductSelectorRef {
  focus: () => void;
}

export const ProductSelector = React.forwardRef<ProductSelectorRef, ProductSelectorProps>(({
  value,
  onValueChange,
  placeholder = "Pilih produk...",
  disabled = false,
  className,
  emptyMessage = "Tidak ada produk ditemukan.",
  searchPlaceholder = "Cari produk...",
  includeInactive = false,
}, ref) => {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState('');
  const [page, setPage] = React.useState(1);
  const [allProducts, setAllProducts] = React.useState<Product[]>([]);

  // Expose focus method via ref
  React.useImperativeHandle(ref, () => ({
    focus: () => {
      setOpen(true);
    }
  }));

  // Debounce search to avoid too many API calls
  const debouncedSearch = useDebounce(searchValue, DEBOUNCE_DELAYS.SEARCH);

  // Query parameters for API call
  const queryParams: ProductQueryParams = React.useMemo(() => ({
    page,
    limit: 20,
    search: debouncedSearch || undefined,
    isActive: includeInactive ? undefined : true,
    sortBy: 'name',
    sortOrder: 'asc',
  }), [page, debouncedSearch, includeInactive]);

  // Fetch products with search and pagination
  const { data: productsResponse, isLoading, error } = useProducts(queryParams);

  // Fetch specific product if we have a value that's not in the loaded products
  const { data: specificProduct } = useProduct(value || '');

  // Helper function to deduplicate products by ID
  const deduplicateProducts = React.useCallback((products: Product[]): Product[] => {
    const seen = new Set<string>();
    return products.filter(product => {
      if (seen.has(product.id)) {
        return false;
      }
      seen.add(product.id);
      return true;
    });
  }, []);

  // Reset page when search changes
  React.useEffect(() => {
    setPage(1);
    // Don't clear allProducts here - let the API response handle it
  }, [debouncedSearch]);

  // Accumulate products for infinite scroll
  React.useEffect(() => {
    if (productsResponse?.data) {
      if (page === 1) {
        // Reset products for new search or initial load
        let newProducts = [...productsResponse.data];

        // If we have a specific product, add it to the beginning
        if (specificProduct) {
          newProducts = [specificProduct, ...newProducts];
        }

        // Deduplicate the entire list
        setAllProducts(deduplicateProducts(newProducts));
      } else {
        // Append products for pagination with deduplication
        setAllProducts(prev => {
          const combined = [...prev, ...productsResponse.data];
          return deduplicateProducts(combined);
        });
      }
    }
  }, [productsResponse, page, deduplicateProducts]);

  // Handle specific product changes separately
  React.useEffect(() => {
    if (specificProduct) {
      setAllProducts(prev => {
        // Check if specific product is already in the list
        const hasSpecificProduct = prev.some(p => p.id === specificProduct.id);

        if (!hasSpecificProduct) {
          // Add specific product to the beginning and deduplicate
          return deduplicateProducts([specificProduct, ...prev]);
        }

        return prev; // No change needed
      });
    }
  }, [specificProduct, deduplicateProducts]);

  // Find the selected product
  const selectedProduct = React.useMemo(() => {
    // First try to find in loaded products
    const foundInList = allProducts.find((product) => product.id === value);

    // If not found in list but we have a specific product, use that
    const finalProduct = foundInList || specificProduct;

    return finalProduct || null;
  }, [allProducts, value, specificProduct]);

  // Load more products when scrolling to bottom
  const loadMore = React.useCallback(() => {
    if (productsResponse?.meta?.hasNextPage && !isLoading) {
      setPage(prev => prev + 1);
    }
  }, [productsResponse?.meta?.hasNextPage, isLoading]);

  const handleSelect = (productId: string) => {
    onValueChange(productId);
    setOpen(false);
    setSearchValue('');
    setPage(1);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchValue('');
      setPage(1);
    }
  };

  const handleSearchChange = (newSearch: string) => {
    setSearchValue(newSearch);
  };

  const clearSearch = () => {
    setSearchValue('');
  };

  // Get display text for the selected product
  const displayText = selectedProduct
    ? `${selectedProduct.name} (${selectedProduct.code})`
    : placeholder;

  // Check if we need to show tooltip (when product is selected and text might be truncated)
  // Using a more conservative threshold based on average character width in text-xs (approximately 6-7px per character)
  // For 160px column width minus padding and icon space (~120px usable), we estimate ~18-20 characters
  const shouldShowTooltip = selectedProduct && displayText.length > 20;

  const triggerButton = (
    <Button
      type="button"
      variant="outline"
      role="combobox"
      aria-expanded={open}
      className={cn(
        "h-9 w-full justify-between text-left font-normal",
        !selectedProduct && "text-muted-foreground",
        className
      )}
      disabled={disabled}
    >
      <span className="truncate">
        {displayText}
      </span>
      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
    </Button>
  );

  return (
    <TooltipProvider>
      <Popover open={open} onOpenChange={handleOpenChange}>
        {shouldShowTooltip ? (
          <Tooltip>
            <TooltipTrigger asChild>
              <PopoverTrigger asChild>
                {triggerButton}
              </PopoverTrigger>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <div className="text-xs">
                <div className="font-medium">{selectedProduct.name}</div>
                <div className="text-muted-foreground">Kode: {selectedProduct.code}</div>
                {selectedProduct.category && (
                  <div className="text-muted-foreground">Kategori: {selectedProduct.category}</div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        ) : (
          <PopoverTrigger asChild>
            {triggerButton}
          </PopoverTrigger>
        )}
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
          <Command shouldFilter={false}>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                placeholder={searchPlaceholder}
                value={searchValue}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                autoFocus
              />
              {searchValue && (
                <button
                  onClick={clearSearch}
                  className="leading-0 ml-2 h-4 w-4 shrink-0 opacity-50 hover:opacity-100 transition-opacity"
                  type="button"
                >
                  ×
                </button>
              )}
            </div>
            <CommandList
              className="max-h-[300px] overflow-y-auto"
              onScroll={(e) => {
                const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
                if (scrollHeight - scrollTop <= clientHeight * 1.5) {
                  loadMore();
                }
              }}
            >
              {isLoading && page === 1 ? (
                <div className="flex items-center justify-center py-6">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2 text-sm text-muted-foreground">Memuat produk...</span>
                </div>
              ) : error ? (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  Gagal memuat produk
                </div>
              ) : allProducts.length === 0 && !isLoading ? (
                <CommandEmpty className="px-4 py-6 text-center text-sm">{emptyMessage}</CommandEmpty>
              ) : (
                <CommandGroup>
                  {searchValue && allProducts.length > 0 && (
                    <div className="px-2 py-1.5 text-xs text-muted-foreground border-b">
                      {allProducts.length} produk ditemukan
                      {productsResponse?.meta?.hasNextPage && " (scroll untuk lebih banyak)"}
                    </div>
                  )}
                  {allProducts.map((product) => (
                    <CommandItem
                      key={product.id}
                      value={product.id}
                      onSelect={() => handleSelect(product.id)}
                      className="cursor-pointer py-2"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex flex-col min-w-0 flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{product.name}</span>
                            <span className="text-xs bg-muted px-1.5 py-0.5 rounded font-mono">
                              {product.code}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mt-0.5">
                            <span className="text-xs text-muted-foreground truncate">
                              {product.category}
                            </span>
                            {product.manufacturer && (
                              <span className="text-xs text-muted-foreground truncate">
                                • {product.manufacturer}
                              </span>
                            )}
                          </div>
                        </div>
                        <Check
                          className={cn(
                            "ml-2 h-4 w-4 shrink-0",
                            value === product.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  ))}
                  {isLoading && page > 1 && (
                    <div className="flex items-center justify-center py-2">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      <span className="ml-2 text-xs text-muted-foreground">Memuat lebih banyak...</span>
                    </div>
                  )}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </TooltipProvider>
  );
});

ProductSelector.displayName = "ProductSelector";
