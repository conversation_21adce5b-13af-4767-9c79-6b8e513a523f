"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, memo } from "react";
import { UseFormReturn, useWatch } from "react-hook-form";
import { Trash2, AlertTriangle, CalendarIcon, RefreshCw, CircleAlert } from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { TableCell, TableRow } from "@/components/ui/table";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ProductSelector } from "@/components/ui/product-selector";
import { LiveCurrencyInput } from "@/components/ui/currency-input";
import { AdvancedBatchNumberInput } from "@/components/ui/advanced-batch-number-input";
import { PurchaseOrder } from "@/types/purchase-order";
import { formatCurrency } from "@/lib/utils";
import { GoodsReceiptFormValues } from "./goods-receipt-schemas";
import { toast } from "sonner";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";

interface GoodsReceiptItemRowProps {
  index: number;
  form: UseFormReturn<GoodsReceiptFormValues>;
  onRemove: () => void;
  selectedPurchaseOrder: PurchaseOrder | null;
  onSubstitutionRequest: (itemIndex: number, originalProductId: string) => void;
  onBatchValidationRequest: (
    itemIndex: number,
    batchNumber: string,
    productId: string,
    productName: string,
  ) => void;
  variant: "table" | "card";
}

const GoodsReceiptItemRowComponent = function GoodsReceiptItemRow({
  index,
  form,
  onRemove,
  selectedPurchaseOrder,
  onSubstitutionRequest,
  onBatchValidationRequest,
  variant,
}: GoodsReceiptItemRowProps) {
  // Optimized watching - use useWatch with specific field paths
  const watchedFields = useWatch({
    control: form.control,
    name: [
      `items.${index}.quantityReceived`,
      `items.${index}.quantityOrdered`,
      `items.${index}.unitPrice`,
      `items.${index}.isSubstitution`,
      `items.${index}.productId`,
      `items.${index}`, // Watch entire item for batch-related fields
      "supplierId", // Watch supplierId for batch validation
    ],
  });

  // Memoized calculations to prevent unnecessary recalculations
  const { quantityReceived, quantityOrdered, unitPrice, isSubstitution, productId, currentItem, supplierId } = useMemo(() => {
    const [qtyReceived, qtyOrdered, price, substitution, prodId, item, supplId] = watchedFields || [];
    return {
      quantityReceived: qtyReceived || 0,
      quantityOrdered: qtyOrdered,
      unitPrice: price || 0,
      isSubstitution: substitution || false,
      productId: prodId || "",
      currentItem: item || {},
      supplierId: supplId || "",
    };
  }, [watchedFields]);

  const totalPrice = useMemo(() => {
    return quantityReceived * unitPrice;
  }, [quantityReceived, unitPrice]);

  // Memoized derived values
  const showPOContext = useMemo(() => quantityOrdered && quantityOrdered > 0, [quantityOrdered]);
  const isProductFromPO = useMemo(() => selectedPurchaseOrder && showPOContext, [selectedPurchaseOrder, showPOContext]);

  const [isProductLocked, setIsProductLocked] = useState(
    isProductFromPO && !isSubstitution,
  );

  useEffect(() => {
    if (isSubstitution) {
      setIsProductLocked(false);
    } else if (isProductFromPO) {
      setIsProductLocked(true);
    }
  }, [isSubstitution, isProductFromPO]);

  const handleProductSubstitution = useCallback(() => {
    const currentProductId = form.getValues(`items.${index}.productId`);
    if (onSubstitutionRequest) {
      onSubstitutionRequest(index, currentProductId);
    } else {
      setIsProductLocked(false);
      form.setValue(`items.${index}.isSubstitution`, true);
      form.setValue(
        `items.${index}.substitutionReason`,
        "supplier_substitution",
      );
      toast.info("Produk dapat diubah.");
    }
  }, [form, index, onSubstitutionRequest]);

  if (variant === "card") {
    return (
      <>
        <Card className="relative w-full p-0 rounded-0 border-none border-b! shadow-none">
          <CardContent className=" space-y-4 p-0">
            {/* Header with Remove Button */}
            <div className="flex items-center justify-between">
              <h4 className="font-bold">
                Item #{index + 1}
              </h4>
              <Button
                type="button"
                variant="outline"
                onClick={onRemove}
                className="text-red-600 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>

            {/* Product Selection */}
            <FormField
              control={form.control}
              name={`items.${index}.productId`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Produk</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <ProductSelector
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Pilih produk..."
                        disabled={!!isProductLocked}
                      />
                      {isProductFromPO && (
                        <div className="flex items-center justify-between">
                          {isProductLocked ? (
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-blue-600">
                                🔒 Dari PO
                              </span>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={handleProductSubstitution}
                                className=" text-orange-600 hover:bg-orange-50"
                              >
                                Substitusi
                              </Button>
                            </div>
                          ) : (
                            <span className="text-xs text-orange-600">
                              ⚠️ Produk Substitusi
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            {/* Quantities and Pricing */}
            <div className="grid grid-cols-2 gap-3">
              {showPOContext && (
                <FormField
                  control={form.control}
                  name={`items.${index}.quantityOrdered`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Qty Dipesan</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          value={field.value || ""}
                          disabled
                          className="text-sm"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}
              <FormField
                control={form.control}
                name={`items.${index}.quantityReceived`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs">Qty Diterima</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="number"
                        min="0"
                        step="1"
                        className="text-sm"
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name={`items.${index}.unitPrice`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs">Harga Satuan</FormLabel>
                    <FormControl>
                      <LiveCurrencyInput
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="0"
                        className="text-sm"
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <div className="flex flex-col justify-end">
                <span className="text-xs text-muted-foreground mb-1">Total</span>
                <span className="text-sm font-medium">
                  {formatCurrency(totalPrice)}
                </span>
              </div>
            </div>

            {/* Batch Information */}
            <div className="space-y-3 pt-2 border-t">
              <FormField
                control={form.control}
                name={`items.${index}.batchNumber`}
                render={({ field, fieldState }) => {
                  return (
                    <FormItem>
                      <FormLabel className="text-xs">Nomor Batch</FormLabel>
                      <FormControl>
                        <AdvancedBatchNumberInput
                          value={field.value || ""}
                          onChange={(val) => {
                            // Normalize batch number to uppercase for consistency
                            field.onChange(val.toUpperCase());
                          }}
                          productId={currentItem?.productId}
                          supplierId={supplierId}
                          expiryDate={
                            currentItem?.expiryDate
                              ? new Date(currentItem.expiryDate)
                              : undefined
                          }
                          manufacturingDate={
                            currentItem?.manufacturingDate
                              ? new Date(currentItem.manufacturingDate)
                              : undefined
                          }
                          placeholder="Masukkan nomor batch..."
                          error={fieldState.error?.message}
                          className="h-auto text-sm"
                          showHistory={true}
                          showFormatGuide={true}
                          onValidationRequest={() => {
                            if (
                              field.value &&
                              currentItem?.productId &&
                              onBatchValidationRequest
                            ) {
                              const productName = `Item ${index + 1}`;
                              onBatchValidationRequest(
                                index,
                                field.value,
                                currentItem.productId,
                                productName,
                              );
                            }
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  );
                }}
              />

              <div className="grid grid-cols-2 gap-3">
                <FormField
                  control={form.control}
                  name={`items.${index}.manufacturingDate`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Tgl Produksi</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal text-sm",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(new Date(field.value), "dd/MM/yy")
                              ) : (
                                <span>Pilih</span>
                              )}
                              <CalendarIcon className="ml-auto h-3 w-3 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={
                              field.value ? new Date(field.value) : undefined
                            }
                            onSelect={(date) => {
                              field.onChange(
                                date ? format(date, "yyyy-MM-dd") : "",
                              );
                            }}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                            }
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`items.${index}.expiryDate`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Tgl Kadaluarsa</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal text-xs",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(new Date(field.value), "dd/MM/yy")
                              ) : (
                                <span>Pilih</span>
                              )}
                              <CalendarIcon className="ml-auto h-3 w-3 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <CalendarComponent
                            mode="single"
                            selected={
                              field.value ? new Date(field.value) : undefined
                            }
                            onSelect={(date) => {
                              field.onChange(
                                date ? format(date, "yyyy-MM-dd") : "",
                              );
                            }}
                            disabled={(date) => date < new Date()}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>
        <Separator />
      </>
    );
  }

  // Table variant
  return (
    <TableRow>
      {/* Product Column */}
      <TableCell className="p-2">
        <FormField
          control={form.control}
          name={`items.${index}.productId`}
          render={({ field }) => (
            <FormItem className="block">
              <FormControl>
                <div className="gap-1 flex items-center">
                  <ProductSelector
                    value={field.value}
                    onValueChange={field.onChange}
                    placeholder="Pilih produk..."
                    disabled={!!isProductLocked}
                    className="text-sm flex-1"
                  />
                  {isProductFromPO && (
                    <div className="flex items-center flex-shrink-0">
                      {isProductLocked ? (
                        <div className="flex items-center gap-2">
                          <Button
                            type="button"
                            variant="outline"

                            onClick={handleProductSubstitution}
                            className="text-xs text-orange-600 hover:bg-orange-50"
                          >
                            <RefreshCw className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              type="button"
                              variant="outline"

                              className="text-xs text-orange-600 hover:bg-orange-50"
                            >
                              <CircleAlert className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            Produk telah disubstitusi
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                  )}

                </div>
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Batch Number */}
      <TableCell className="p-2">
        <FormField
          control={form.control}
          name={`items.${index}.batchNumber`}
          render={({ field, fieldState }) => {
            return (
              <AdvancedBatchNumberInput
                value={field.value || ""}
                onChange={(val) => {
                  field.onChange(val.toUpperCase())
                }}
                productId={currentItem?.productId}
                supplierId={supplierId}
                expiryDate={
                  currentItem?.expiryDate
                    ? new Date(currentItem.expiryDate)
                    : undefined
                }
                manufacturingDate={
                  currentItem?.manufacturingDate
                    ? new Date(currentItem.manufacturingDate)
                    : undefined
                }
                placeholder="Batch..."
                error={fieldState.error?.message}
                showHistory={false}
                showFormatGuide={false}
                onValidationRequest={() => {
                  if (
                    field.value &&
                    currentItem?.productId &&
                    onBatchValidationRequest
                  ) {
                    const productName = `Item ${index + 1}`;
                    onBatchValidationRequest(
                      index,
                      field.value,
                      currentItem.productId,
                      productName,
                    );
                  }
                }}
              />
            );
          }}
        />
      </TableCell>

      {/* Quantity Ordered */}
      <TableCell className="p-2">
        <FormField
          control={form.control}
          name={`items.${index}.quantityOrdered`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  {...field}
                  value={field.value || ""}
                  className="w-full text-sm"
                  disabled
                  placeholder={showPOContext ? "" : "N/A"}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </TableCell>

      {/* Quantity Received */}
      <TableCell className="p-2">
        <FormField
          control={form.control}
          name={`items.${index}.quantityReceived`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  {...field}
                  type="number"
                  min="0"
                  step="1"
                  className="w-full text-sm"
                  onChange={(e) => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Unit Price */}
      <TableCell className="p-2">
        <FormField
          control={form.control}
          name={`items.${index}.unitPrice`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <LiveCurrencyInput
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder="0"
                  className="text-sm"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Manufacturing Date */}
      <TableCell className="p-2">
        <FormField
          control={form.control}
          name={`items.${index}.manufacturingDate`}
          render={({ field }) => (
            <FormItem>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full pl-3 text-left font-normal text-xs",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      {field.value ? (
                        format(new Date(field.value), "dd/MM/yy")
                      ) : (
                        <span>Pilih</span>
                      )}
                      <CalendarIcon className="ml-auto h-3 w-3 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={
                      field.value ? new Date(field.value) : undefined
                    }
                    onSelect={(date) => {
                      field.onChange(
                        date ? format(date, "yyyy-MM-dd") : "",
                      );
                    }}
                    disabled={(date) =>
                      date > new Date() || date < new Date("1900-01-01")
                    }
                  />
                </PopoverContent>
              </Popover>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Expiry Date */}
      <TableCell className="p-2">
        <FormField
          control={form.control}
          name={`items.${index}.expiryDate`}
          render={({ field }) => (
            <FormItem>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full pl-3 text-left font-normal text-xs",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      {field.value ? (
                        format(new Date(field.value), "dd/MM/yy")
                      ) : (
                        <span>Pilih</span>
                      )}
                      <CalendarIcon className="ml-auto h-3 w-3 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={
                      field.value ? new Date(field.value) : undefined
                    }
                    onSelect={(date) => {
                      field.onChange(
                        date ? format(date, "yyyy-MM-dd") : "",
                      );
                    }}
                    disabled={(date) => date < new Date()}
                  />
                </PopoverContent>
              </Popover>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Total */}
      <TableCell className="p-2 text-sm font-medium text-right">
        {formatCurrency(totalPrice)}
      </TableCell>

      {/* Actions */}
      <TableCell className="p-2 text-center">
        <Button
          type="button"
          variant="ghost"
          onClick={onRemove}
          className=" text-red-600 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
};

// Export memoized component to prevent unnecessary re-renders
export const GoodsReceiptItemRow = memo(GoodsReceiptItemRowComponent);
