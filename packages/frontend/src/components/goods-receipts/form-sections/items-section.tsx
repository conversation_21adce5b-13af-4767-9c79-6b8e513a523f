"use client";

import { UseFormReturn, useFieldArray, useWatch } from "react-hook-form";
import { Plus, Trash2 } from "lucide-react";
import { useMemo, memo, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PurchaseOrder } from "@/types/purchase-order";
import { formatCurrency } from "@/lib/utils";
import { GoodsReceiptFormValues } from "./goods-receipt-schemas";
import { GoodsReceiptItemRow } from "./goods-receipt-item-row";
import { Input } from "@/components/ui/input";
import { ProductSelector } from "@/components/ui/product-selector";

interface ItemsSectionProps {
  form: UseFormReturn<GoodsReceiptFormValues>;
  selectedPurchaseOrder: PurchaseOrder | null;
  onSubstitutionRequest: (itemIndex: number, originalProductId: string) => void;
  onBatchValidationRequest: (
    itemIndex: number,
    batchNumber: string,
    productId: string,
    productName: string,
  ) => void;
}

const ItemsSectionComponent = function ItemsSection({
  form,
  selectedPurchaseOrder,
  onSubstitutionRequest,
  onBatchValidationRequest,
}: ItemsSectionProps) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch items for total calculation - optimized approach
  const watchedItems = useWatch({
    control: form.control,
    name: "items",
  });

  // Memoized total calculation to prevent unnecessary recalculations
  const totalAmount = useMemo(() => {
    if (!watchedItems || !Array.isArray(watchedItems)) return 0;
    return watchedItems.reduce((sum, item) => {
      const quantityReceived = item?.quantityReceived || 0;
      const unitPrice = item?.unitPrice || 0;
      return sum + quantityReceived * unitPrice;
    }, 0);
  }, [watchedItems]);

  // Memoized handlers to prevent unnecessary re-renders
  const handleAddItem = useCallback(() => {
    append({
      productId: "",
      unitId: "",
      quantityOrdered: undefined,
      quantityReceived: 0,
      quantityAccepted: 0,
      quantityRejected: 0,
      unitPrice: 0,
      batchNumber: "",
      expiryDate: "",
      manufacturingDate: "",
      storageLocation: "",
      storageCondition: "",
      conditionOnReceipt: "good",
      damageNotes: "",

      notes: "",
      isSubstitution: false,
      originalProductId: "",
      substitutionReason: "",
      substitutionNotes: "",
    });
  }, [append]);

  const handleRemoveItem = useCallback((index: number) => {
    remove(index);
  }, [remove]);

  return (
    <Card className="flex-1 shadow-none border-0 p-0 md:py-6 md:shadow-xl md:border">
      <CardContent className="p-0 md:px-6">
        <div className="flex flex-col h-full">
          {/* Header with Add Button */}
          <div className="flex items-center justify-between border-b pb-4 mb-4 md:mb-0">
            <div className="md:flex items-center gap-4">
              <h3 className="text-lg font-medium">Daftar Item</h3>
              <span className="text-sm text-muted-foreground">
                {fields.length} item • Total: {formatCurrency(totalAmount)}
              </span>
            </div>
            <Button
              type="button"
              onClick={handleAddItem}
              size="sm"
              className="h-8"
            >
              <Plus className="h-4 w-4 mr-1" />
              Tambah Item
            </Button>
          </div>

          {/* Items Table - Mobile Responsive */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full">
              <div className="hidden md:block border-b">
                {/* Desktop Table */}
                <Table>
                  <TableHeader className="sticky top-0 z-10">
                    <TableRow>
                      <TableHead className="w-[180px] text-sm p-3">
                        Produk & Batch
                      </TableHead>
                      <TableHead className="w-[150px] max-w-[150px] text-sm p-3">
                        No. Batch
                      </TableHead>
                      <TableHead className="w-[60px] whitespace-normal text-sm p-3">
                        Qty Dipesan
                      </TableHead>
                      <TableHead className="w-[60px] whitespace-normal text-sm p-3">
                        Qty Diterima
                      </TableHead>
                      <TableHead className="w-[120px] whitespace-normal text-sm p-3">
                        Harga Satuan
                      </TableHead>
                      <TableHead className="w-[60px] whitespace-normal text-right text-sm p-3">
                        Total
                      </TableHead>
                      <TableHead className="w-[60px] text-center text-sm p-3">
                        Aksi
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fields.map((field, index) => (
                      <GoodsReceiptItemRow
                        key={field.id}
                        index={index}
                        form={form}
                        onRemove={() => handleRemoveItem(index)}
                        selectedPurchaseOrder={selectedPurchaseOrder}
                        onSubstitutionRequest={onSubstitutionRequest}
                        onBatchValidationRequest={onBatchValidationRequest}
                        variant="table"
                      />
                    ))}
                    {fields.length === 0 && (
                      <TableRow>
                        <TableCell
                          colSpan={6}
                          className="text-center py-8 text-muted-foreground"
                        >
                          Belum ada item. Klik "Tambah Item" untuk menambahkan.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Cards */}
              <div className="md:hidden space-y-4">
                {fields.map((field, index) => (
                  <GoodsReceiptItemRow
                    key={field.id}
                    index={index}
                    form={form}
                    onRemove={() => handleRemoveItem(index)}
                    selectedPurchaseOrder={selectedPurchaseOrder}
                    onSubstitutionRequest={onSubstitutionRequest}
                    onBatchValidationRequest={onBatchValidationRequest}
                    variant="card"
                  />
                ))}
                {fields.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    Belum ada item. Klik "Tambah Item" untuk menambahkan.
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Export memoized component to prevent unnecessary re-renders
export const ItemsSection = memo(ItemsSectionComponent);
