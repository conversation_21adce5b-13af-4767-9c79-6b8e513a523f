"use client";

import { UseFormReturn, useFieldArray, useWatch } from "react-hook-form";
import { Plus, Trash2 } from "lucide-react";
import { useMemo, memo, useCallback, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PurchaseOrder } from "@/types/purchase-order";
import { formatCurrency } from "@/lib/utils";
import { GoodsReceiptFormValues } from "./goods-receipt-schemas";
import { GoodsReceiptItemRow } from "./goods-receipt-item-row";
import { Input } from "@/components/ui/input";
import { ProductSelector } from "@/components/ui/product-selector";

interface ItemsSectionProps {
  form: UseFormReturn<GoodsReceiptFormValues>;
  selectedPurchaseOrder: PurchaseOrder | null;
  onSubstitutionRequest: (itemIndex: number, originalProductId: string) => void;
  onBatchValidationRequest: (
    itemIndex: number,
    batchNumber: string,
    productId: string,
    productName: string,
  ) => void;
}

const ItemsSectionComponent = function ItemsSection({
  form,
  selectedPurchaseOrder,
  onSubstitutionRequest,
  onBatchValidationRequest,
}: ItemsSectionProps) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // State for confirmation dialog
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    itemIndex: number;
  }>({
    isOpen: false,
    itemIndex: -1,
  });

  // Watch items for total calculation - optimized approach
  const watchedItems = useWatch({
    control: form.control,
    name: "items",
  });

  // Memoized total calculation to prevent unnecessary recalculations
  const totalAmount = useMemo(() => {
    if (!watchedItems || !Array.isArray(watchedItems)) return 0;
    return watchedItems.reduce((sum, item) => {
      const quantityReceived = item?.quantityReceived || 0;
      const unitPrice = item?.unitPrice || 0;
      return sum + quantityReceived * unitPrice;
    }, 0);
  }, [watchedItems]);

  // Memoized handlers to prevent unnecessary re-renders
  const handleAddItem = useCallback(() => {
    append({
      productId: "",
      unitId: "",
      quantityOrdered: undefined,
      quantityReceived: 0,
      unitPrice: 0,
      batchNumber: "",
      expiryDate: "",
      manufacturingDate: "",
      storageLocation: "",
      storageCondition: "",
      conditionOnReceipt: "good",
      damageNotes: "",
      notes: "",
      isSubstitution: false,
      originalProductId: "",
      substitutionReason: "",
      substitutionNotes: "",
    });
  }, [append]);

  const handleRemoveItem = useCallback((index: number) => {
    setConfirmDialog({
      isOpen: true,
      itemIndex: index,
    });
  }, []);

  const handleConfirmRemove = useCallback(() => {
    if (confirmDialog.itemIndex >= 0) {
      remove(confirmDialog.itemIndex);
    }
    setConfirmDialog({
      isOpen: false,
      itemIndex: -1,
    });
  }, [confirmDialog.itemIndex, remove]);

  const handleCancelRemove = useCallback(() => {
    setConfirmDialog({
      isOpen: false,
      itemIndex: -1,
    });
  }, []);

  return (
    <Card className="flex-1 shadow-none border-0 p-0 md:py-6 md:shadow-xl md:border">
      <CardContent className="p-0 md:px-6">
        <div className="flex flex-col h-full">
          {/* Header with Add Button */}
          <div className="flex items-center justify-between border-b pb-4 mb-4 md:mb-0">
            <div className="md:flex items-center gap-4">
              <h3 className="text-lg font-medium">Daftar Item</h3>
              <span className="text-sm text-muted-foreground">
                {fields.length} item • Total: {formatCurrency(totalAmount)}
              </span>
            </div>
            <Button
              type="button"
              onClick={handleAddItem}
              size="sm"
              className="h-8"
            >
              <Plus className="h-4 w-4 mr-1" />
              Tambah Item
            </Button>
          </div>

          {/* Items Table - Mobile Responsive */}
          <div className="flex-1 overflow-hidden">
            {/* Desktop Table with Horizontal Scrolling */}
            <div className="hidden md:block border-b">
              <div className="relative overflow-hidden">
                {/* Horizontal scroll container */}
                <div className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                  <Table className="min-w-[1400px] relative">
                    <TableHeader className="sticky top-0 z-20 bg-background">
                      <TableRow>
                        <TableHead className="w-[200px] text-sm p-3 bg-background">
                          Produk
                        </TableHead>
                        <TableHead className="w-[150px] text-sm p-3 bg-background">
                          No. Batch
                        </TableHead>
                        <TableHead className="w-[100px] whitespace-normal text-sm p-3 bg-background">
                          Qty Dipesan
                        </TableHead>
                        <TableHead className="w-[100px] whitespace-normal text-sm p-3 bg-background">
                          Qty Diterima
                        </TableHead>
                        <TableHead className="w-[120px] whitespace-normal text-sm p-3 bg-background">
                          Harga Satuan
                        </TableHead>
                        <TableHead className="w-[120px] whitespace-normal text-sm p-3 bg-background">
                          Tgl Produksi
                        </TableHead>
                        <TableHead className="w-[120px] whitespace-normal text-sm p-3 bg-background">
                          Tgl Kadaluarsa
                        </TableHead>
                        <TableHead className="w-[100px] whitespace-normal text-right text-sm p-3 bg-background">
                          Total
                        </TableHead>
                        <TableHead className="w-[80px] text-center text-sm p-3 sticky right-0 bg-background z-30 border-l-2 border-border shadow-[-8px_0_16px_-4px_rgba(0,0,0,0.1)]">
                          Aksi
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                        <TableBody>
                          {fields.map((field, index) => (
                            <GoodsReceiptItemRow
                              key={field.id}
                              index={index}
                              form={form}
                              onRemove={() => handleRemoveItem(index)}
                              selectedPurchaseOrder={selectedPurchaseOrder}
                              onSubstitutionRequest={onSubstitutionRequest}
                              onBatchValidationRequest={onBatchValidationRequest}
                              variant="table"
                            />
                          ))}
                          {fields.length === 0 && (
                            <TableRow>
                              <TableCell
                                colSpan={9}
                                className="text-center py-8 text-muted-foreground"
                              >
                                Belum ada item. Klik "Tambah Item" untuk menambahkan.
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </ScrollArea>
                  {/* Horizontal scroll indicator gradient */}
                  <div className="absolute top-0 right-0 w-8 h-full bg-gradient-to-l from-background to-transparent pointer-events-none z-10" />
                </div>
              </div>

              {/* Mobile Cards */}
              <div className="md:hidden space-y-4">
                {fields.map((field, index) => (
                  <GoodsReceiptItemRow
                    key={field.id}
                    index={index}
                    form={form}
                    onRemove={() => handleRemoveItem(index)}
                    selectedPurchaseOrder={selectedPurchaseOrder}
                    onSubstitutionRequest={onSubstitutionRequest}
                    onBatchValidationRequest={onBatchValidationRequest}
                    variant="card"
                  />
                ))}
                {fields.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    Belum ada item. Klik "Tambah Item" untuk menambahkan.
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </div>
      </CardContent>

      {/* Confirmation Dialog */}
      <AlertDialog open={confirmDialog.isOpen} onOpenChange={(open) => !open && handleCancelRemove()}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus Item</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus item ini? Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelRemove}>
              Batal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmRemove}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

// Export memoized component to prevent unnecessary re-renders
export const ItemsSection = memo(ItemsSectionComponent);
