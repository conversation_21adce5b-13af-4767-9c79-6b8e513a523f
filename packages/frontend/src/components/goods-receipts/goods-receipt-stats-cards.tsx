'use client';

import {
  Package,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Zap,
  BarChart3,
  Timer
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { useGoodsReceiptStats } from '@/hooks/useGoodsReceipts';
import { formatCurrency } from '@/lib/utils';

interface GoodsReceiptStatsCardsProps {
  period?: string;
}

export function GoodsReceiptStatsCards({ period }: GoodsReceiptStatsCardsProps = {}) {
  const { data: stats, isLoading, error } = useGoodsReceiptStats(period);

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        <Card className="col-span-full">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <AlertTriangle className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground mb-1">Gagal memuat statistik penerimaan barang</p>
            <p className="text-xs text-muted-foreground">
              {error instanceof Error ? error.message : 'Terjadi kesalahan yang tidak diketahui'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5">
        {Array.from({ length: 10 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  // Ensure all required fields have safe defaults to prevent runtime errors

  const safeStats = {
    totalReceipts: stats.totalReceipts ?? 0,
    pendingInspection: stats.pendingInspection ?? 0,
    approved: stats.approved ?? 0,
    rejected: stats.rejected ?? 0,
    totalValue: stats.totalValue ?? 0,
    processingTimeAnalytics: {
      averageProcessingTime: stats.processingTimeAnalytics?.averageProcessingTime ?? 0,
      totalReceipts: stats.processingTimeAnalytics?.totalReceipts ?? 0,
      efficiencyMetrics: {
        fastProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.fastProcessing ?? 0,
        slowProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.slowProcessing ?? 0,
        averageProcessing: stats.processingTimeAnalytics?.efficiencyMetrics?.averageProcessing ?? 0,
        fastPercentage: stats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0,
        slowPercentage: stats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0,
      },
    },
    volumeTrends: stats.volumeTrends ?? [],
  };

  // Helper functions for analytics with additional safety checks

  const getVolumeTrend = () => {
    if (!safeStats.volumeTrends || safeStats.volumeTrends.length < 2) return null;
    const recent = safeStats.volumeTrends.slice(-2);
    const current = recent[1]?.count || 0;
    const previous = recent[0]?.count || 0;
    return current > previous ? 'up' : current < previous ? 'down' : 'stable';
  };

  const volumeTrend = getVolumeTrend();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Receipts with Volume Trend */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Penerimaan</CardTitle>
          <div className="flex items-center gap-1">
            {volumeTrend === 'up' && <TrendingUp className="h-3 w-3 text-green-600" />}
            {volumeTrend === 'down' && <TrendingDown className="h-3 w-3 text-red-600" />}
            <Package className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{safeStats.totalReceipts.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            {volumeTrend === 'up' && '↗ Meningkat dari periode sebelumnya'}
            {volumeTrend === 'down' && '↘ Menurun dari periode sebelumnya'}
            {(!volumeTrend || volumeTrend === 'stable') && 'Semua penerimaan barang'}
          </p>
        </CardContent>
      </Card>

      {/* Total Value */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Nilai</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(safeStats.totalValue)}</div>
          <p className="text-xs text-muted-foreground">
            Nilai total penerimaan barang
          </p>
        </CardContent>
      </Card>

      {/* Pending Inspection with Alert */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Menunggu Inspeksi</CardTitle>
          <div className="flex items-center gap-1">
            {safeStats.pendingInspection > 10 && <AlertTriangle className="h-3 w-3 text-orange-600" />}
            <Clock className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">
            {safeStats.pendingInspection.toLocaleString()}
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              Perlu kontrol kualitas
            </p>
            {safeStats.pendingInspection > 10 && (
              <Badge variant="outline" className="text-xs">
                Prioritas Tinggi
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Approved */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Disetujui</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {safeStats.approved.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Penerimaan yang disetujui
          </p>
        </CardContent>
      </Card>

      {/* Processing Efficiency */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Efisiensi Proses</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0).toFixed(0)}%
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              Proses cepat (&lt;24 jam)
            </p>
            {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.fastPercentage ?? 0) >= 70 && (
              <Badge variant="outline" className="text-xs text-green-600">
                Efisien
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Average Processing Time */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Rata-rata Proses</CardTitle>
          <Timer className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {Math.round(safeStats.processingTimeAnalytics?.averageProcessingTime ?? 0)}h
          </div>
          <p className="text-xs text-muted-foreground">
            Waktu rata-rata pemrosesan
          </p>
        </CardContent>
      </Card>

      {/* Rejected (if any) */}
      {safeStats.rejected > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ditolak</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {safeStats.rejected.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Tidak memenuhi standar
            </p>
          </CardContent>
        </Card>
      )}

      {/* Volume Analytics */}
      {safeStats.volumeTrends && safeStats.volumeTrends.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tren Volume</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {safeStats.volumeTrends.slice(-1)[0]?.count || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Penerimaan hari terakhir
            </p>
          </CardContent>
        </Card>
      )}

      {/* Slow Processing Alert (if significant) */}
      {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0) > 20 && (
        <Card className="border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Proses Lambat</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {(safeStats.processingTimeAnalytics?.efficiencyMetrics?.slowPercentage ?? 0).toFixed(0)}%
            </div>
            <div className="flex items-center gap-2">
              <p className="text-xs text-muted-foreground">
                Proses &gt;72 jam
              </p>
              <Badge variant="outline" className="text-xs text-orange-600">
                Perhatian
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
