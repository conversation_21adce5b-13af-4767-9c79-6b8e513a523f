'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, RefreshCw, Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Product, CreateProductDto, UpdateProductDto, ProductType, ProductCategory, MedicineClassification } from '@/types/product';
import { useCreateProduct, useUpdateProduct } from '@/hooks/useProducts';
import { useBaseUnits } from '@/hooks/useProductUnits';
import {
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_CATEGORY_OPTIONS,
  MEDICINE_CLASSIFICATION_OPTIONS,
  getMedicineClassificationSymbol,
  getMedicineClassificationDescription,
} from '@/lib/constants/product';
import { UnitHierarchyManager } from './UnitHierarchyManager';
import { UnitSelector } from '@/components/ui/unit-selector';
import { productsApi } from '@/lib/api/products';
import { navigateBackToProducts } from '@/lib/utils/navigation';
import { useDebouncedValidation, DEBOUNCE_DELAYS } from '@/lib/utils/debounce';

// Form validation schema
const productFormSchema = z.object({
  code: z.string().min(1, 'Kode produk wajib diisi'),
  name: z.string().min(1, 'Nama produk wajib diisi'),
  genericName: z.string().optional(),
  type: z.nativeEnum(ProductType),
  category: z.nativeEnum(ProductCategory),
  manufacturer: z.string().optional(),
  bpomNumber: z.string().optional(),
  medicineClassification: z.nativeEnum(MedicineClassification),
  regulatorySymbol: z.string().optional(),
  baseUnitId: z.string().min(1, 'Unit dasar wajib dipilih'),
  minimumStock: z.number().min(0).optional(),
  maximumStock: z.number().min(0).optional(),
  reorderPoint: z.number().min(0).optional(),
  description: z.string().optional(),
  activeIngredient: z.string().optional(),
  strength: z.string().optional(),
  dosageForm: z.string().optional(),
  indication: z.string().optional(),
  contraindication: z.string().optional(),
  sideEffects: z.string().optional(),
  dosage: z.string().optional(),
  storage: z.string().optional(),
  notes: z.string().optional(),
  unitHierarchies: z.array(z.object({
    id: z.string().optional(),
    unitId: z.string(),
    level: z.number(),
    conversionFactor: z.number().min(1),
    costPrice: z.number().optional(),
    sellingPrice: z.number().optional(),
  })).optional(),
});

type ProductFormData = z.infer<typeof productFormSchema>;

interface ProductFormClientProps {
  product?: Product;
}

export function ProductFormClient({ product }: ProductFormClientProps) {
  const router = useRouter();
  const isEditing = !!product;

  const [selectedType, setSelectedType] = useState<ProductType>(product?.type || ProductType.MEDICINE);
  const [selectedClassification, setSelectedClassification] = useState<MedicineClassification>(
    product?.medicineClassification || MedicineClassification.NON_MEDICINE
  );

  // Product code generation state
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isValidatingCode, setIsValidatingCode] = useState(false);
  const [codeValidation, setCodeValidation] = useState<{ isUnique: boolean; message: string } | null>(null);

  // Mutations
  const createProductMutation = useCreateProduct();
  const updateProductMutation = useUpdateProduct();

  // Load base units
  const { data: baseUnits = [], isLoading: isLoadingUnits } = useBaseUnits();

  // Form setup
  const form = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      code: product?.code || '',
      name: product?.name || '',
      genericName: product?.genericName || '',
      type: product?.type || ProductType.MEDICINE,
      category: product?.category || ProductCategory.OTHER,
      manufacturer: product?.manufacturer || '',
      bpomNumber: product?.bpomNumber || '',
      medicineClassification: product?.medicineClassification || MedicineClassification.NON_MEDICINE,
      regulatorySymbol: product?.regulatorySymbol || '',
      baseUnitId: product?.baseUnitId || '',
      minimumStock: product?.minimumStock || undefined,
      maximumStock: product?.maximumStock || undefined,
      reorderPoint: product?.reorderPoint || undefined,
      description: product?.description || '',
      activeIngredient: product?.activeIngredient || '',
      strength: product?.strength || '',
      dosageForm: product?.dosageForm || '',
      indication: product?.indication || '',
      contraindication: product?.contraindication || '',
      sideEffects: product?.sideEffects || '',
      dosage: product?.dosage || '',
      storage: product?.storage || '',
      notes: product?.notes || '',
      unitHierarchies: product?.unitHierarchies || [],
    },
  });

  // Watch form values for dynamic updates
  const watchedType = form.watch('type');
  const watchedClassification = form.watch('medicineClassification');

  useEffect(() => {
    setSelectedType(watchedType);
  }, [watchedType]);

  useEffect(() => {
    setSelectedClassification(watchedClassification);
  }, [watchedClassification]);

  // Auto-generate regulatory symbol based on classification
  useEffect(() => {
    const symbol = getMedicineClassificationSymbol(selectedClassification);
    const description = getMedicineClassificationDescription(selectedClassification);
    if (symbol && description) {
      form.setValue('regulatorySymbol', description);
    }
  }, [selectedClassification, form]);

  // Auto-generate product code when type changes (only for new products)
  useEffect(() => {
    if (!isEditing && selectedType) {
      handleGenerateCode();
    }
  }, [selectedType, isEditing]);

  // Product code generation functions
  const handleGenerateCode = async () => {
    if (!selectedType) return;

    setIsGeneratingCode(true);
    try {
      const response = await productsApi.generateProductCode(selectedType);
      form.setValue('code', response.code);
      setCodeValidation(null);
    } catch (error) {
      console.error('Error generating product code:', error);
    } finally {
      setIsGeneratingCode(false);
    }
  };

  const validateProductCode = async (code: string) => {
    if (!code || code.length < 3) {
      setCodeValidation(null);
      return;
    }

    setIsValidatingCode(true);
    try {
      const response = await productsApi.validateProductCode(code);
      setCodeValidation({
        isUnique: response.isUnique,
        message: response.isUnique ? 'Kode tersedia' : 'Kode sudah digunakan'
      });
    } catch (error) {
      console.error('Error validating product code:', error);
      setCodeValidation({
        isUnique: false,
        message: 'Error validating code'
      });
    } finally {
      setIsValidatingCode(false);
    }
  };

  // Debounced validation function
  const debouncedValidateProductCode = useDebouncedValidation(
    validateProductCode,
    DEBOUNCE_DELAYS.VALIDATION
  );

  // Watch code changes for validation
  const watchedCode = form.watch('code');
  useEffect(() => {
    if (watchedCode && !isEditing) {
      debouncedValidateProductCode(watchedCode);
    }
  }, [watchedCode, isEditing, debouncedValidateProductCode]);

  const onSubmit = async (data: ProductFormData) => {
    try {
      if (isEditing && product) {
        const updateData: UpdateProductDto = {
          ...data,
          minimumStock: data.minimumStock || undefined,
          maximumStock: data.maximumStock || undefined,
          reorderPoint: data.reorderPoint || undefined,
        };

        await updateProductMutation.mutateAsync({
          id: product.id,
          data: updateData,
        });

        router.push(`/dashboard/products/${product.id}`);
      } else {
        const createData: CreateProductDto = {
          ...data,
          minimumStock: data.minimumStock || undefined,
          maximumStock: data.maximumStock || undefined,
          reorderPoint: data.reorderPoint || undefined,
        };

        const newProduct = await createProductMutation.mutateAsync(createData);
        router.push(`/dashboard/products/${newProduct.id}`);
      }
    } catch (error) {
      console.error('Error saving product:', error);
    }
  };

  const isLoading = createProductMutation.isPending || updateProductMutation.isPending;
  const isMedicine = selectedType === ProductType.MEDICINE;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigateBackToProducts(router)}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Button>

          <Button type="submit" disabled={isLoading} className="w-full sm:w-auto">
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? 'Menyimpan...' : isEditing ? 'Perbarui' : 'Simpan'}
          </Button>
        </div>

        <div className="grid gap-6 xl:grid-cols-3">
          {/* Main Form */}
          <div className="xl:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Informasi Dasar</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Kode Produk *</FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input
                              placeholder="Masukkan kode produk"
                              {...field}
                              className={codeValidation ? (codeValidation.isUnique ? 'border-green-500' : 'border-red-500') : ''}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleGenerateCode}
                            disabled={isGeneratingCode || !selectedType}
                            className="shrink-0"
                          >
                            {isGeneratingCode ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <RefreshCw className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        {/* Validation feedback */}
                        {(isValidatingCode || codeValidation) && (
                          <div className="flex items-center gap-1 text-xs mt-1">
                            {isValidatingCode ? (
                              <>
                                <RefreshCw className="h-3 w-3 animate-spin" />
                                <span className="text-muted-foreground">Memvalidasi...</span>
                              </>
                            ) : codeValidation ? (
                              <>
                                {codeValidation.isUnique ? (
                                  <Check className="h-3 w-3 text-green-600" />
                                ) : (
                                  <X className="h-3 w-3 text-red-600" />
                                )}
                                <span className={codeValidation.isUnique ? 'text-green-600' : 'text-red-600'}>
                                  {codeValidation.message}
                                </span>
                              </>
                            ) : null}
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nama Produk *</FormLabel>
                        <FormControl>
                          <Input placeholder="Masukkan nama produk" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="genericName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nama Generik</FormLabel>
                        <FormControl>
                          <Input placeholder="Masukkan nama generik" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Jenis Produk *</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            setSelectedType(value as ProductType);
                          }}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih jenis produk" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {PRODUCT_TYPE_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Kategori *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih kategori" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {PRODUCT_CATEGORY_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="manufacturer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Produsen</FormLabel>
                        <FormControl>
                          <Input placeholder="Masukkan nama produsen" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Deskripsi</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Masukkan deskripsi produk"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Medicine Information - Only show for medicine products */}
            {isMedicine && (
              <Card>
                <CardHeader>
                  <CardTitle>Informasi Obat</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="medicineClassification"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Klasifikasi Obat *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih klasifikasi obat" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {MEDICINE_CLASSIFICATION_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div className="flex items-center gap-2">
                                  {option.symbol && <span>{option.symbol}</span>}
                                  <span>{option.label}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {selectedClassification !== MedicineClassification.NON_MEDICINE && (
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className="text-xs">
                              {getMedicineClassificationSymbol(selectedClassification)} {' '}
                              {MEDICINE_CLASSIFICATION_OPTIONS.find(opt => opt.value === selectedClassification)?.label}
                            </Badge>
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-4 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="bpomNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nomor BPOM</FormLabel>
                          <FormControl>
                            <Input placeholder="DKL1234567890A1" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="activeIngredient"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kandungan Aktif</FormLabel>
                          <FormControl>
                            <Input placeholder="Paracetamol 500mg" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="strength"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kekuatan</FormLabel>
                          <FormControl>
                            <Input placeholder="500mg" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dosageForm"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bentuk Sediaan</FormLabel>
                          <FormControl>
                            <Input placeholder="Tablet, Kapsul, Sirup" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="indication"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Indikasi</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Demam, sakit kepala, nyeri ringan hingga sedang"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contraindication"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kontraindikasi</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Hipersensitif terhadap paracetamol"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="sideEffects"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Efek Samping</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Jarang terjadi efek samping pada dosis normal"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dosage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Dosis</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Dewasa: 1-2 tablet, 3-4 kali sehari"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="storage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Penyimpanan</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Simpan di tempat sejuk dan kering, terhindar dari cahaya langsung"
                              className="min-h-[60px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Unit Hierarchy Management - Moved from sidebar for better space utilization */}
            {form.watch('baseUnitId') && (
              <UnitHierarchyManager
                baseUnitId={form.watch('baseUnitId')}
                unitHierarchies={form.watch('unitHierarchies') || []}
                onChange={(hierarchies) => form.setValue('unitHierarchies', hierarchies)}
                errors={Object.fromEntries(
                  Object.entries(form.formState.errors).map(([key, error]) => [
                    key,
                    error?.message || 'Field error'
                  ])
                )}
              />
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Unit & Stock Information */}
            <Card>
              <CardHeader>
                <CardTitle>Unit & Stok</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="baseUnitId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit Dasar *</FormLabel>
                      <FormControl>
                        <UnitSelector
                          units={baseUnits}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder={isLoadingUnits ? "Memuat unit..." : "Pilih unit dasar"}
                          searchPlaceholder="Cari unit dasar..."
                          emptyMessage="Tidak ada unit dasar tersedia."
                          disabled={isLoadingUnits}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Separator />

                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="minimumStock"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Stok Minimum</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="maximumStock"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Stok Maksimum</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="reorderPoint"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Titik Reorder</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Additional Notes */}
            <Card>
              <CardHeader>
                <CardTitle>Catatan</CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Catatan Tambahan</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Catatan atau informasi tambahan tentang produk"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </Form>
  );
}
