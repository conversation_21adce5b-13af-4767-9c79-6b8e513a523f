'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useDebounce, DEBOUNCE_DELAYS } from '@/hooks/useDebounce';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, Barcode, Plus, Package } from 'lucide-react';
import { productsApi } from '@/lib/api/products';
import { formatCurrency } from '@/lib/utils';
import { ProductType } from '@/types/product';

interface PosProductSearchProps {
  onAddToCart: (product: any, unit: any, quantity: number) => void;
}

export function PosProductSearch({ onAddToCart }: PosProductSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('ALL');
  const debouncedSearchTerm = useDebounce(searchTerm, DEBOUNCE_DELAYS.SEARCH);

  // Query for products
  const { data: productsData, isLoading } = useQuery({
    queryKey: ['pos-products', debouncedSearchTerm, selectedCategory],
    queryFn: () => productsApi.getProducts({
      search: debouncedSearchTerm,
      isActive: true,
      type: selectedCategory !== 'ALL' ? selectedCategory as ProductType : undefined,
      limit: 20,
    }),
    staleTime: 1000 * 60, // 1 minute
  });

  const products = productsData?.data || [];

  const handleAddToCart = (product: any) => {
    // Find the base unit (or first available unit)
    const baseUnit = product.unitHierarchies?.find(
      (uh: any) => uh.level === 0
    ) || product.unitHierarchies?.[0];

    if (baseUnit) {
      onAddToCart(product, {
        id: baseUnit.unitId,
        name: baseUnit.unit.name,
        sellingPrice: baseUnit.sellingPrice
      }, 1);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Cari produk (nama, kode)"
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button variant="outline" className="gap-2">
          <Barcode className="h-4 w-4" />
          Scan
        </Button>
      </div>

      <Tabs defaultValue="ALL" onValueChange={setSelectedCategory}>
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="ALL">Semua</TabsTrigger>
          <TabsTrigger value="MEDICINE">Obat</TabsTrigger>
          <TabsTrigger value="MEDICAL_DEVICE">Alkes</TabsTrigger>
          <TabsTrigger value="GENERAL">Umum</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="border rounded-md overflow-hidden">
        <div className="bg-muted p-2 font-medium flex items-center">
          <Package className="mr-2 h-4 w-4" />
          Hasil Pencarian
          {!isLoading && (
            <Badge variant="outline" className="ml-2">
              {products.length} produk
            </Badge>
          )}
        </div>

        <div className="max-h-[300px] overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
              <span>Mencari produk...</span>
            </div>
          ) : products.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              {debouncedSearchTerm
                ? 'Tidak ada produk yang sesuai dengan pencarian'
                : 'Masukkan kata kunci untuk mencari produk'}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 p-2">
              {products.map((product) => {
                const baseUnit = product.unitHierarchies?.find(
                  (uh: any) => uh.level === 0
                ) || product.unitHierarchies?.[0];

                const price = baseUnit?.sellingPrice || 0;
                const unitName = baseUnit?.unit?.name || '';

                return (
                  <Card
                    key={product.id}
                    className="p-3 flex items-center justify-between cursor-pointer hover:bg-accent"
                    onClick={() => handleAddToCart(product)}
                  >
                    <div>
                      <div className="font-medium">{product.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {product.code} · {formatCurrency(price)}/{unitName}
                      </div>
                    </div>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 